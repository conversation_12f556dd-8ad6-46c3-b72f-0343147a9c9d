package com.crypto.trading.market.service.impl;

import com.crypto.trading.market.manager.WebSocketManager;
import com.crypto.trading.market.service.MarketDataService;
import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.listener.DepthDataListener;
import com.crypto.trading.market.listener.KlineDataListener;
import com.crypto.trading.market.listener.TradeDataListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * 市场数据服务实现类
 * 负责启动和管理市场数据的WebSocket连接
 *
 * <AUTHOR>
 * @create 2024-07-26
 */
@Service
public class MarketDataServiceImpl implements MarketDataService {

    private static final Logger log = LoggerFactory.getLogger(MarketDataServiceImpl.class);

    @Autowired
    private WebSocketManager webSocketManager;
    
    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private KlineDataListener klineDataListener;

    @Autowired
    private DepthDataListener depthDataListener;

    @Autowired
    private TradeDataListener tradeDataListener;

    /**
     * 启动市场数据服务
     * 主要是启动WebSocket连接，订阅市场数据
     */
    @Override
    @PostConstruct
    public void start() {
        log.info("启动市场数据服务...");
        try {
            // 启动K线数据监听器
            Thread.startVirtualThread(() -> {
                try {
                    log.info("启动K线数据监听器...");
                    klineDataListener.startKlineDataSubscription();
                    log.info("K线数据监听器启动成功");
                } catch (Exception e) {
                    log.error("启动K线数据监听器异常", e);
                }
            });

            // 启动深度数据监听器
            Thread.startVirtualThread(() -> {
                try {
                    log.info("启动深度数据监听器...");
                    depthDataListener.startDepthDataSubscription();
                    log.info("深度数据监听器启动成功");
                } catch (Exception e) {
                    log.error("启动深度数据监听器异常", e);
                }
            });

            // 启动交易数据监听器
            Thread.startVirtualThread(() -> {
                try {
                    log.info("启动交易数据监听器...");
                    tradeDataListener.startTradeDataSubscription();
                    log.info("交易数据监听器启动成功");
                } catch (Exception e) {
                    log.error("启动交易数据监听器异常", e);
                }
            });
            
            // 启动WebSocket监听器
            webSocketManager.startListeners();
            log.info("市场数据服务启动成功");
        } catch (Exception e) {
            log.error("启动市场数据服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("启动市场数据服务失败", e);
        }
    }

    /**
     * 停止市场数据服务
     * 关闭WebSocket连接
     */
    @Override
    @PreDestroy
    public void stop() {
        log.info("停止市场数据服务...");
        try {
            // 停止K线数据监听器
            klineDataListener.stopKlineDataSubscription();
            log.info("K线数据监听器已停止");

            // 停止深度数据监听器
            depthDataListener.stopDepthDataSubscription();
            log.info("深度数据监听器已停止");

            // 停止交易数据监听器
            tradeDataListener.stopTradeDataSubscription();
            log.info("交易数据监听器已停止");
            
            // 停止WebSocket监听器
            webSocketManager.stopListeners();
            log.info("市场数据服务停止成功");
        } catch (Exception e) {
            log.error("停止市场数据服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("停止市场数据服务失败", e);
        }
    }

    /**
     * 重启市场数据服务
     * 先停止，然后再启动
     */
    @Override
    public void restart() {
        log.info("重启市场数据服务...");
        try {
            stop();
            // 等待一段时间确保连接完全关闭
            Thread.sleep(1000);
            start();
            log.info("市场数据服务重启成功");
        } catch (Exception e) {
            log.error("重启市场数据服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("重启市场数据服务失败", e);
        }
    }

    /**
     * 获取服务状态
     *
     * @return 服务是否运行中
     */
    @Override
    public boolean isRunning() {
        return webSocketManager.isRunning();
    }
    
    /**
     * 获取市场数据配置
     *
     * @return 市场数据配置信息
     */
    public MarketDataConfig getMarketDataConfig() {
        return marketDataConfig;
    }
}