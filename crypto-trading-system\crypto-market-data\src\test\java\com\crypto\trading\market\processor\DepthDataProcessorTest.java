package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.quality.Strictness;
import org.mockito.junit.jupiter.MockitoSettings;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * DepthDataProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DepthDataProcessorTest {

    @Mock
    private KafkaMessageProducer kafkaMessageProducer;

    @Mock
    private InfluxDBRepository influxDBRepository;

    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private MarketDataConverter marketDataConverter;
    
    @Mock
    private ExecutorService virtualThreadExecutor;

    @InjectMocks
    private DepthDataProcessor depthDataProcessor;

    private WebSocketMessage<DepthData> depthMessage;
    private DepthDataDTO depthDataDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据
        DepthData depthData = new DepthData();
        
        // 创建WebSocketMessage包装DepthData
        depthMessage = new WebSocketMessage<>("depth", 1622535659999L, "BTCUSDT", depthData);
        
        // 创建DepthDataDTO
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("34950.00"), new BigDecimal("1.5")));
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("34900.00"), new BigDecimal("2.5")));
        
        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("35050.00"), new BigDecimal("1.0")));
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("35100.00"), new BigDecimal("2.0")));
        
        depthDataDTO = new DepthDataDTO(
            "BTCUSDT", 
            123456789L, 
            20, 
            bids, 
            asks, 
            LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535659999L), ZoneId.systemDefault())
        );

        // 配置mock行为，使用doReturn语法
        doReturn("{\"test\":\"json\"}").when(objectMapper).writeValueAsString(any(DepthDataDTO.class));
        doReturn(depthDataDTO).when(marketDataConverter).convertToOrderBookDTO(any(DepthData.class), eq("BTCUSDT"));
        
        // 配置虚拟线程执行器，立即执行提交的任务
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(virtualThreadExecutor).submit(any(Runnable.class));
    }

    @Test
    void testProcessDepthData() throws Exception {
        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证发布到Kafka
        verify(kafkaMessageProducer).sendDepthData(eq("BTCUSDT"), eq("{\"test\":\"json\"}"));

        // 验证存储到InfluxDB
        verify(influxDBRepository).saveOrderBookData(eq(depthDataDTO));
    }

    @Test
    void testProcessDepthDataWithKafkaException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("Kafka error")).when(kafkaMessageProducer).sendDepthData(any(), any());

        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证即使Kafka发布失败，也会尝试存储到InfluxDB
        verify(influxDBRepository).saveOrderBookData(eq(depthDataDTO));
    }

    @Test
    void testProcessDepthDataWithInfluxDBException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("InfluxDB error")).when(influxDBRepository).saveOrderBookData(any());

        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证即使InfluxDB存储失败，也会尝试发布到Kafka
        verify(kafkaMessageProducer).sendDepthData(eq("BTCUSDT"), eq("{\"test\":\"json\"}"));
    }

    @Test
    void testProcessDepthDataWithJsonException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("JSON error")).when(objectMapper).writeValueAsString(any(DepthDataDTO.class));

        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证即使JSON序列化失败，也会尝试存储到InfluxDB
        verify(influxDBRepository).saveOrderBookData(eq(depthDataDTO));
        // 验证不会尝试发布到Kafka
        verify(kafkaMessageProducer, never()).sendDepthData(any(), any());
    }
} 