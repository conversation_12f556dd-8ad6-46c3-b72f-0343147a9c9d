{"text": "2025-06-22 21:49:38.887 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:12.723611", "seconds": 12.723611}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.887069+08:00", "timestamp": **********.887069}}}
{"text": "2025-06-22 21:49:38.926 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:12.762619", "seconds": 12.762619}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.926077+08:00", "timestamp": **********.926077}}}
{"text": "2025-06-22 21:49:38.931 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:12.767729", "seconds": 12.767729}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.931187+08:00", "timestamp": **********.931187}}}
{"text": "2025-06-22 21:49:38.932 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:12.768728", "seconds": 12.768728}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.932186+08:00", "timestamp": **********.932186}}}
{"text": "2025-06-22 21:49:38.932 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:12.769272", "seconds": 12.769272}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.932730+08:00", "timestamp": **********.93273}}}
{"text": "2025-06-22 21:49:38.932 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:12.769272", "seconds": 12.769272}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.932730+08:00", "timestamp": **********.93273}}}
{"text": "2025-06-22 21:49:38.935 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:12.772505", "seconds": 12.772505}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.935963+08:00", "timestamp": **********.935963}}}
{"text": "2025-06-22 21:49:38.935 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:12.772505", "seconds": 12.772505}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.935963+08:00", "timestamp": **********.935963}}}
{"text": "2025-06-22 21:49:38.944 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:12.781256", "seconds": 12.781256}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.944714+08:00", "timestamp": **********.944714}}}
{"text": "2025-06-22 21:49:38.946 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:12.783258", "seconds": 12.783258}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:38.946716+08:00", "timestamp": **********.946716}}}
{"text": "2025-06-22 21:49:44.348 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:18.184664", "seconds": 18.184664}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:49:44.348122+08:00", "timestamp": 1750600184.348122}}}
{"text": "2025-06-22 21:50:05.485 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:39.322433", "seconds": 39.322433}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.485891+08:00", "timestamp": 1750600205.485891}}}
{"text": "2025-06-22 21:50:05.485 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:39.322433", "seconds": 39.322433}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.485891+08:00", "timestamp": 1750600205.485891}}}
{"text": "2025-06-22 21:50:05.545 | INFO | data.clients.mysql_client:_connect:130 | MySQL连接建立成功\n", "record": {"elapsed": {"repr": "0:00:39.381664", "seconds": 39.381664}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 130, "message": "MySQL连接建立成功", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.545122+08:00", "timestamp": 1750600205.545122}}}
{"text": "2025-06-22 21:50:05.559 | INFO | data.clients.mysql_client:_init_tables:187 | 数据库表初始化完成\n", "record": {"elapsed": {"repr": "0:00:39.396159", "seconds": 39.396159}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_init_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 187, "message": "数据库表初始化完成", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.559617+08:00", "timestamp": 1750600205.559617}}}
{"text": "2025-06-22 21:50:05.560 | INFO | data.clients.mysql_client:__init__:103 | MySQL客户端初始化完成: localhost:3306/crypto_trading\n", "record": {"elapsed": {"repr": "0:00:39.397165", "seconds": 39.397165}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 103, "message": "MySQL客户端初始化完成: localhost:3306/crypto_trading", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.560623+08:00", "timestamp": 1750600205.560623}}}
{"text": "2025-06-22 21:50:05.576 | INFO | ml.models.versioning:__init__:56 | 模型版本管理器初始化完成\n", "record": {"elapsed": {"repr": "0:00:39.413518", "seconds": 39.413518}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "模型版本管理器初始化完成", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.576976+08:00", "timestamp": 1750600205.576976}}}
{"text": "2025-06-22 21:50:05.576 | INFO | ml.models.online_learner:__init__:102 | 在线学习器初始化完成\n", "record": {"elapsed": {"repr": "0:00:39.413518", "seconds": 39.413518}, "exception": null, "extra": {"component": "OnlineLearner"}, "file": {"name": "online_learner.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\online_learner.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 102, "message": "在线学习器初始化完成", "module": "online_learner", "name": "ml.models.online_learner", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.576976+08:00", "timestamp": 1750600205.576976}}}
{"text": "2025-06-22 21:50:05.577 | INFO | strategy.base:__init__:54 | 基础策略初始化: UnifiedMLStrategy\n", "record": {"elapsed": {"repr": "0:00:39.414519", "seconds": 39.414519}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "base.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\base.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 54, "message": "基础策略初始化: UnifiedMLStrategy", "module": "base", "name": "strategy.base", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.577977+08:00", "timestamp": 1750600205.577977}}}
{"text": "2025-06-22 21:50:05.577 | INFO | strategy.unified_ml:__init__:86 | 统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']\n", "record": {"elapsed": {"repr": "0:00:39.414519", "seconds": 39.414519}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.577977+08:00", "timestamp": 1750600205.577977}}}
{"text": "2025-06-22 21:50:05.578 | INFO | __main__:start:89 | 启动交易策略应用\n", "record": {"elapsed": {"repr": "0:00:39.415519", "seconds": 39.415519}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 89, "message": "启动交易策略应用", "module": "main", "name": "__main__", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:05.578977+08:00", "timestamp": 1750600205.578977}}}
{"text": "2025-06-22 21:50:05.581 | INFO | api.http_server:run_server:206 | 启动HTTP API服务器: http://localhost:9530\n", "record": {"elapsed": {"repr": "0:00:39.417914", "seconds": 39.417914}, "exception": null, "extra": {}, "file": {"name": "http_server.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\api\\http_server.py"}, "function": "run_server", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 206, "message": "启动HTTP API服务器: http://localhost:9530", "module": "http_server", "name": "api.http_server", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 5988, "name": "Thread-1 (run_server)"}, "time": {"repr": "2025-06-22 21:50:05.581372+08:00", "timestamp": 1750600205.581372}}}
{"text": "2025-06-22 21:50:07.585 | INFO | api.http_server:start_server:230 | HTTP API服务器已启动在端口 9530\n", "record": {"elapsed": {"repr": "0:00:41.421576", "seconds": 41.421576}, "exception": null, "extra": {}, "file": {"name": "http_server.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\api\\http_server.py"}, "function": "start_server", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 230, "message": "HTTP API服务器已启动在端口 9530", "module": "http_server", "name": "api.http_server", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:07.585034+08:00", "timestamp": 1750600207.585034}}}
{"text": "2025-06-22 21:50:07.585 | INFO | __main__:start:95 | HTTP API服务器启动成功\n", "record": {"elapsed": {"repr": "0:00:41.421576", "seconds": 41.421576}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 95, "message": "HTTP API服务器启动成功", "module": "main", "name": "__main__", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:07.585034+08:00", "timestamp": 1750600207.585034}}}
{"text": "2025-06-22 21:50:07.601 | INFO | __main__:start:107 | 未找到有效模型，开始训练...\n", "record": {"elapsed": {"repr": "0:00:41.437806", "seconds": 41.437806}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 107, "message": "未找到有效模型，开始训练...", "module": "main", "name": "__main__", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:07.601264+08:00", "timestamp": 1750600207.601264}}}
{"text": "2025-06-22 21:50:07.601 | INFO | data.real_data_loader:__init__:60 | 数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']\n", "record": {"elapsed": {"repr": "0:00:41.438311", "seconds": 41.438311}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 60, "message": "数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:07.601769+08:00", "timestamp": 1750600207.601769}}}
{"text": "2025-06-22 21:50:07.601 | INFO | __main__:train_model:204 | 加载训练数据...\n", "record": {"elapsed": {"repr": "0:00:41.438311", "seconds": 41.438311}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 204, "message": "加载训练数据...", "module": "main", "name": "__main__", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:07.601769+08:00", "timestamp": 1750600207.601769}}}
{"text": "2025-06-22 21:50:07.602 | INFO | data.real_data_loader:load_training_data:86 | 开始加载训练数据: 2025-03-24 21:50:07.602775 到 2025-06-22 21:50:07.602775\n", "record": {"elapsed": {"repr": "0:00:41.439317", "seconds": 41.439317}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "开始加载训练数据: 2025-03-24 21:50:07.602775 到 2025-06-22 21:50:07.602775", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:07.602775+08:00", "timestamp": 1750600207.602775}}}
{"text": "2025-06-22 21:50:31.065 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1m, 记录数: 87646\n", "record": {"elapsed": {"repr": "0:01:04.901712", "seconds": 64.901712}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1m, 记录数: 87646", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:31.065170+08:00", "timestamp": 1750600231.06517}}}
{"text": "2025-06-22 21:50:35.656 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_5m, 记录数: 17540\n", "record": {"elapsed": {"repr": "0:01:09.492732", "seconds": 69.492732}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_5m, 记录数: 17540", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:35.656190+08:00", "timestamp": 1750600235.65619}}}
{"text": "2025-06-22 21:50:37.382 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_15m, 记录数: 5842\n", "record": {"elapsed": {"repr": "0:01:11.219540", "seconds": 71.21954}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_15m, 记录数: 5842", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:37.382998+08:00", "timestamp": 1750600237.382998}}}
{"text": "2025-06-22 21:50:37.858 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1h, 记录数: 1433\n", "record": {"elapsed": {"repr": "0:01:11.694591", "seconds": 71.694591}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1h, 记录数: 1433", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:37.858049+08:00", "timestamp": 1750600237.858049}}}
{"text": "2025-06-22 21:50:38.004 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_4h, 记录数: 319\n", "record": {"elapsed": {"repr": "0:01:11.840552", "seconds": 71.840552}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_4h, 记录数: 319", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:38.004010+08:00", "timestamp": 1750600238.00401}}}
{"text": "2025-06-22 21:50:38.074 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1d, 记录数: 10\n", "record": {"elapsed": {"repr": "0:01:11.911163", "seconds": 71.911163}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1d, 记录数: 10", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:50:38.074621+08:00", "timestamp": 1750600238.074621}}}
{"text": "2025-06-22 21:51:00.804 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1m, 记录数: 86955\n", "record": {"elapsed": {"repr": "0:01:34.641481", "seconds": 94.641481}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1m, 记录数: 86955", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:51:00.804939+08:00", "timestamp": 1750600260.804939}}}
{"text": "2025-06-22 21:51:05.313 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_5m, 记录数: 17516\n", "record": {"elapsed": {"repr": "0:01:39.150332", "seconds": 99.150332}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_5m, 记录数: 17516", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:51:05.313790+08:00", "timestamp": 1750600265.31379}}}
{"text": "2025-06-22 21:51:06.852 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_15m, 记录数: 5840\n", "record": {"elapsed": {"repr": "0:01:40.689002", "seconds": 100.689002}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_15m, 记录数: 5840", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:51:06.852460+08:00", "timestamp": 1750600266.85246}}}
{"text": "2025-06-22 21:51:07.275 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1h, 记录数: 1433\n", "record": {"elapsed": {"repr": "0:01:41.112243", "seconds": 101.112243}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1h, 记录数: 1433", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:51:07.275701+08:00", "timestamp": 1750600267.275701}}}
{"text": "2025-06-22 21:51:07.413 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_4h, 记录数: 320\n", "record": {"elapsed": {"repr": "0:01:41.249701", "seconds": 101.249701}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_4h, 记录数: 320", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:51:07.413159+08:00", "timestamp": 1750600267.413159}}}
{"text": "2025-06-22 21:51:07.475 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1d, 记录数: 10\n", "record": {"elapsed": {"repr": "0:01:41.311881", "seconds": 101.311881}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1d, 记录数: 10", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 17520, "name": "MainProcess"}, "thread": {"id": 37136, "name": "MainThread"}, "time": {"repr": "2025-06-22 21:51:07.475339+08:00", "timestamp": 1750600267.475339}}}
