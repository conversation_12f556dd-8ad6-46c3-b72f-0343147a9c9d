# 基础依赖
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0
scipy==1.11.1
matplotlib==3.7.2
seaborn==0.12.2

# 深度学习
torch==2.0.1
tensorflow==2.13.0
transformers==4.30.2

# 数据处理和分析
statsmodels==0.14.0
ta==0.10.2
prophet==1.1.4
lppls==0.6.14

# 消息队列
kafka-python==2.0.2
confluent-kafka==2.1.1

# 数据库连接
influxdb-client==1.36.1
pymysql==1.1.0
sqlalchemy==2.0.19

# 性能优化
numba==0.57.1
dask==2023.6.0

# 其他工具
tqdm==4.65.0
python-dotenv==1.0.0
loguru==0.7.0
pyyaml==6.0
configparser==5.3.0
pytest==7.3.1
pylint==2.17.4
black==23.3.0

# 自定义依赖
# deepseek-core==1.2.0  # 在项目中使用本地实现