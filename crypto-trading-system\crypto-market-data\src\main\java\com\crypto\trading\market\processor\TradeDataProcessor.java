package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

/**
 * 交易数据处理器
 * 负责处理交易数据，包括发布到Kafka和存储到InfluxDB和MySQL
 * 使用JDK21虚拟线程提高性能
 */
@Component
public class TradeDataProcessor {

    private static final Logger log = LoggerFactory.getLogger(TradeDataProcessor.class);

    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;

    @Autowired
    private InfluxDBRepository influxDBRepository;
    
    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MarketDataConverter marketDataConverter;
    
    /**
     * 虚拟线程执行器
     * 通过配置注入，避免每个处理器创建自己的执行器
     */
    @Autowired
    private ExecutorService virtualThreadExecutor;

    /**
     * 处理交易数据
     *
     * @param message WebSocket聚合交易消息
     */
    public void processTradeData(String message) {
        if (message == null) {
            log.warn("收到空的交易数据消息");
            return;
        }

        try {
            // 使用虚拟线程异步处理
            virtualThreadExecutor.submit(() -> {
                try {
                    // 转换为TradeDTO
                    TradeDTO tradeDTO = marketDataConverter.convertAggTradeMessageToTradeDTO(message);
                    
                    if (tradeDTO == null) {
                        log.warn("无法解析交易数据消息: {}", message);
                        return;
                    }
                    
                    log.debug("处理交易数据: symbol={}, tradeId={}, price={}, quantity={}",
                            tradeDTO.getSymbol(), tradeDTO.getId(), 
                            tradeDTO.getPrice(), tradeDTO.getQuantity());
                    
                    // 提高系统容错性，即使一个操作失败，其他操作仍会执行
                    // 发布到Kafka
                    try {
                        publishToKafka(tradeDTO);
                    } catch (Exception e) {
                        log.error("发布交易数据到Kafka异常，但将继续处理: symbol={}, tradeId={}, error={}", 
                                tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
                    }
                    
                    // 存储到InfluxDB
                    try {
                        storeToInfluxDB(tradeDTO);
                    } catch (Exception e) {
                        log.error("存储交易数据到InfluxDB异常，但将继续处理: symbol={}, tradeId={}, error={}", 
                                tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
                    }
                    
                    // 存储到MySQL
                    try {
                        storeToMySQL(tradeDTO);
                    } catch (Exception e) {
                        log.error("存储交易数据到MySQL异常: symbol={}, tradeId={}, error={}", 
                                tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
                    }
                } catch (Exception e) {
                    log.error("处理交易数据异常: message={}, error={}", message, e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("提交交易数据处理任务异常，但应用程序将继续运行: error={}", e.getMessage(), e);
        }
    }

    /**
     * 发布交易数据到Kafka
     *
     * @param tradeDTO 交易数据DTO
     */
    private void publishToKafka(TradeDTO tradeDTO) {
        try {
            // 将交易数据转换为JSON
            String message = objectMapper.writeValueAsString(tradeDTO);

            // 使用交易对作为消息键
            String key = tradeDTO.getSymbol();

            // 发布到Kafka
            kafkaMessageProducer.sendTradeData(key, message);

            log.debug("交易数据已发布到Kafka: symbol={}, tradeId={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId());
        } catch (Exception e) {
            log.error("发布交易数据到Kafka异常: symbol={}, tradeId={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
        }
    }

    /**
     * 存储交易数据到InfluxDB
     *
     * @param tradeDTO 交易数据DTO
     */
    private void storeToInfluxDB(TradeDTO tradeDTO) {
        try {
            // 存储到InfluxDB
            influxDBRepository.saveTradeData(tradeDTO);

            log.debug("交易数据已存储到InfluxDB: symbol={}, tradeId={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId());
        } catch (Exception e) {
            log.error("存储交易数据到InfluxDB异常: symbol={}, tradeId={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * 存储交易数据到MySQL
     *
     * @param tradeDTO 交易数据DTO
     */
    private void storeToMySQL(TradeDTO tradeDTO) {
        try {
            // 存储到MySQL
            mySQLMarketDataRepository.saveTradeData(tradeDTO);

            log.debug("交易数据已存储到MySQL: symbol={}, tradeId={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId());
        } catch (Exception e) {
            log.error("存储交易数据到MySQL异常: symbol={}, tradeId={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
        }
    }
}