#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HTTP API服务器，为Java模块提供健康检查和控制接口
"""

import asyncio
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger


class StrategyHttpServer:
    """策略HTTP服务器"""
    
    def __init__(self, host: str = "localhost", port: int = 9530):
        """
        初始化HTTP服务器
        
        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        self.host = host
        self.port = port
        self.app = FastAPI(
            title="Crypto ML Strategy API",
            description="虚拟货币机器学习策略服务API",
            version="1.0.0"
        )
        self.server_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.start_time = datetime.now()
        self.strategy_status = "initializing"  # initializing, running, stopped, error
        self.last_heartbeat = datetime.now()
        
        # 设置路由
        self._setup_routes()
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/")
        async def root():
            """根路径"""
            return {
                "service": "Crypto ML Strategy",
                "version": "1.0.0",
                "status": self.strategy_status,
                "uptime": str(datetime.now() - self.start_time)
            }
        
        @self.app.get("/status")
        async def get_status():
            """
            健康检查端点 - Java模块会调用此端点检查服务状态
            """
            try:
                current_time = datetime.now()
                uptime_seconds = (current_time - self.start_time).total_seconds()
                
                status_data = {
                    "status": self.strategy_status,
                    "timestamp": current_time.isoformat(),
                    "uptime_seconds": uptime_seconds,
                    "uptime": str(current_time - self.start_time),
                    "last_heartbeat": self.last_heartbeat.isoformat(),
                    "service_info": {
                        "name": "Crypto ML Strategy",
                        "version": "1.0.0",
                        "host": self.host,
                        "port": self.port
                    }
                }
                
                # 更新心跳时间
                self.last_heartbeat = current_time
                
                logger.debug(f"健康检查请求 - 状态: {self.strategy_status}")
                return JSONResponse(content=status_data, status_code=200)
                
            except Exception as e:
                logger.error(f"健康检查异常: {str(e)}")
                return JSONResponse(
                    content={
                        "status": "error",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    },
                    status_code=500
                )
        
        @self.app.post("/init")
        async def initialize_service():
            """
            初始化服务端点 - Java模块会调用此端点初始化服务
            """
            try:
                logger.info("收到服务初始化请求")
                
                # 执行初始化逻辑
                self.strategy_status = "running"
                self.last_heartbeat = datetime.now()
                
                return JSONResponse(
                    content={
                        "status": "success",
                        "message": "服务初始化成功",
                        "service_status": self.strategy_status,
                        "timestamp": datetime.now().isoformat()
                    },
                    status_code=200
                )
                
            except Exception as e:
                logger.error(f"服务初始化异常: {str(e)}")
                self.strategy_status = "error"
                return JSONResponse(
                    content={
                        "status": "error",
                        "message": f"服务初始化失败: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    },
                    status_code=500
                )
        
        @self.app.post("/stop")
        async def stop_service():
            """停止服务端点"""
            try:
                logger.info("收到服务停止请求")
                self.strategy_status = "stopped"
                
                return JSONResponse(
                    content={
                        "status": "success",
                        "message": "服务停止成功",
                        "timestamp": datetime.now().isoformat()
                    },
                    status_code=200
                )
                
            except Exception as e:
                logger.error(f"服务停止异常: {str(e)}")
                return JSONResponse(
                    content={
                        "status": "error",
                        "message": f"服务停止失败: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    },
                    status_code=500
                )
        
        @self.app.get("/health")
        async def health_check():
            """详细健康检查"""
            try:
                current_time = datetime.now()
                uptime_seconds = (current_time - self.start_time).total_seconds()
                
                # 检查服务是否健康（心跳时间不超过5分钟）
                heartbeat_age = (current_time - self.last_heartbeat).total_seconds()
                is_healthy = heartbeat_age < 300 and self.strategy_status in ["running", "initializing"]
                
                health_data = {
                    "healthy": is_healthy,
                    "status": self.strategy_status,
                    "uptime_seconds": uptime_seconds,
                    "last_heartbeat_age_seconds": heartbeat_age,
                    "timestamp": current_time.isoformat(),
                    "checks": {
                        "heartbeat": heartbeat_age < 300,
                        "status": self.strategy_status in ["running", "initializing"]
                    }
                }
                
                status_code = 200 if is_healthy else 503
                return JSONResponse(content=health_data, status_code=status_code)
                
            except Exception as e:
                logger.error(f"详细健康检查异常: {str(e)}")
                return JSONResponse(
                    content={
                        "healthy": False,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    },
                    status_code=500
                )
    
    def start_server(self):
        """启动HTTP服务器（在单独线程中）"""
        if self.is_running:
            logger.warning("HTTP服务器已经在运行中")
            return
        
        def run_server():
            """在线程中运行服务器"""
            try:
                logger.info(f"启动HTTP API服务器: http://{self.host}:{self.port}")
                self.is_running = True
                self.strategy_status = "running"
                
                # 使用uvicorn运行FastAPI应用
                uvicorn.run(
                    self.app,
                    host=self.host,
                    port=self.port,
                    log_level="info",
                    access_log=False  # 减少日志噪音
                )
                
            except Exception as e:
                logger.error(f"HTTP服务器启动失败: {str(e)}")
                self.strategy_status = "error"
                self.is_running = False
        
        # 在单独线程中启动服务器
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        logger.info(f"HTTP API服务器已启动在端口 {self.port}")
    
    def stop_server(self):
        """停止HTTP服务器"""
        if not self.is_running:
            return
        
        logger.info("正在停止HTTP API服务器...")
        self.is_running = False
        self.strategy_status = "stopped"
        
        if self.server_thread and self.server_thread.is_alive():
            # 注意：uvicorn服务器需要通过其他方式停止
            # 这里只是标记状态，实际的服务器停止需要外部处理
            pass
    
    def update_status(self, status: str):
        """更新策略状态"""
        self.strategy_status = status
        self.last_heartbeat = datetime.now()
        logger.debug(f"策略状态更新: {status}")
    
    def is_server_running(self) -> bool:
        """检查服务器是否在运行"""
        return self.is_running and (self.server_thread is not None and self.server_thread.is_alive())


# 全局服务器实例
_server_instance: Optional[StrategyHttpServer] = None


def get_server_instance() -> StrategyHttpServer:
    """获取全局服务器实例"""
    global _server_instance
    if _server_instance is None:
        _server_instance = StrategyHttpServer()
    return _server_instance


def start_http_server(host: str = "localhost", port: int = 9530) -> StrategyHttpServer:
    """启动HTTP服务器"""
    server = get_server_instance()
    if not server.is_server_running():
        server.start_server()
    return server


def stop_http_server():
    """停止HTTP服务器"""
    global _server_instance
    if _server_instance:
        _server_instance.stop_server()


if __name__ == "__main__":
    # 测试服务器
    server = start_http_server()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("收到中断信号，停止服务器")
        stop_http_server()
