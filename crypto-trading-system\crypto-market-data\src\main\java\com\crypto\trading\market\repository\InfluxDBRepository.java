package com.crypto.trading.market.repository;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.config.InfluxDBConfig;
import com.crypto.trading.market.config.MarketDataConfig;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApi;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * InfluxDB数据仓库
 * 负责将市场数据存储到InfluxDB时序数据库中
 * 使用JDK21虚拟线程和批量写入提高性能
 */
@Repository
public class InfluxDBRepository {

    private static final Logger log = LoggerFactory.getLogger(InfluxDBRepository.class);

    @Autowired
    private InfluxDBClient influxDBClient;

    @Autowired
    private InfluxDBConfig influxDBConfig;

    @Autowired
    private MarketDataConfig marketDataConfig;
    
    @Autowired
    private ExecutorService virtualThreadExecutor;

    /**
     * 写入API
     */
    private WriteApi writeApi;
    
    /**
     * 查询API
     */
    private QueryApi queryApi;

    /**
     * 数据点队列
     * 用于批量写入操作
     */
    private final LinkedBlockingQueue<Point> pointQueue = new LinkedBlockingQueue<>();

    /**
     * 定时执行器
     * 用于定时刷新数据到InfluxDB
     */
    private ScheduledExecutorService scheduledExecutorService;

    /**
     * 初始化
     * 创建写入API和定时任务
     */
    @PostConstruct
    public void init() {
        log.info("初始化InfluxDBRepository...");
        
        // 检查InfluxDB客户端是否可用
        if (influxDBClient == null) {
            log.warn("InfluxDB客户端不可用，数据将不会写入InfluxDB，但应用程序将继续运行");
        } else {
            try {
                // 创建写入API
                writeApi = influxDBClient.makeWriteApi();
                log.info("InfluxDB写入API创建成功");
                
                // 创建查询API
                queryApi = influxDBClient.getQueryApi();
                log.info("InfluxDB查询API创建成功");
            } catch (Exception e) {
                log.error("创建InfluxDB API时发生异常: {}", e.getMessage(), e);
                log.warn("InfluxDB功能将被禁用，但应用程序将继续运行");
            }
        }
        
        // 创建定时执行器
        scheduledExecutorService = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r, "influxdb-batch-writer");
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动定时批处理任务
        scheduledExecutorService.scheduleAtFixedRate(
                this::triggerBatchProcess,
                marketDataConfig.getInfluxDbFlushInterval(),
                marketDataConfig.getInfluxDbFlushInterval(),
                TimeUnit.MILLISECONDS);
        
        log.info("InfluxDBRepository初始化完成，批处理大小：{}，刷新间隔：{}ms",
                marketDataConfig.getInfluxDbBatchSize(), 
                marketDataConfig.getInfluxDbFlushInterval());
    }

    /**
     * 触发批处理
     * 使用虚拟线程执行批处理操作
     */
    private void triggerBatchProcess() {
        if (!pointQueue.isEmpty()) {
            // 使用虚拟线程处理批量写入，避免阻塞定时任务线程
            virtualThreadExecutor.submit(this::processBatch);
        }
    }

    /**
     * 销毁
     * 关闭定时任务和写入API
     */
    @PreDestroy
    public void destroy() {
        log.info("关闭InfluxDBRepository...");
        
        // 停止定时执行器
        if (scheduledExecutorService != null) {
            scheduledExecutorService.shutdown();
            try {
                if (!scheduledExecutorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 处理剩余的数据点
        processBatch();
        
        // 关闭写入API
        if (writeApi != null) {
            writeApi.close();
        }
        
        log.info("InfluxDBRepository已关闭");
    }

    /**
     * 存储K线数据到InfluxDB
     *
     * @param klineDataDTO K线数据DTO
     */
    public void saveKlineData(KlineDataDTO klineDataDTO) {
        try {
            // 检查写入API是否可用
            if (writeApi == null) {
                log.warn("InfluxDB写入API不可用，K线数据将不会写入: symbol={}, interval={}", 
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval());
                return;
            }
            
            // 创建数据点
            Point point = Point.measurement("kline")
                    .addTag("symbol", klineDataDTO.getSymbol())
                    .addTag("interval", klineDataDTO.getInterval())
                    .addField("open", klineDataDTO.getOpen())
                    .addField("high", klineDataDTO.getHigh())
                    .addField("low", klineDataDTO.getLow())
                    .addField("close", klineDataDTO.getClose())
                    .addField("volume", klineDataDTO.getVolume())
                    .addField("quote_volume", klineDataDTO.getQuoteAssetVolume())
                    .addField("trades", klineDataDTO.getNumberOfTrades())
                    .addField("closed", klineDataDTO.isClosed() ? 1 : 0)
                    .time(klineDataDTO.getCloseTime().toInstant(ZoneOffset.UTC), WritePrecision.MS);
            
            // 添加到队列
            pointQueue.offer(point);
            
            // 如果队列超过批处理大小，则触发批处理
            if (pointQueue.size() >= marketDataConfig.getInfluxDbBatchSize()) {
                virtualThreadExecutor.submit(this::processBatch);
            }
        } catch (Exception e) {
            log.error("存储K线数据到InfluxDB异常: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
        }
    }

    /**
     * 存储订单簿数据到InfluxDB
     *
     * @param depthDataDTO 订单簿DTO
     */
    public void saveOrderBookData(DepthDataDTO depthDataDTO) {
        try {
            // 创建数据点
            Point point = Point.measurement("depth")
                    .addTag("symbol", depthDataDTO.getSymbol())
                    .addField("last_update_id", depthDataDTO.getLastUpdateId())
                    .time(depthDataDTO.getUpdateTime().toInstant(ZoneOffset.UTC), WritePrecision.NS);
            
            // 添加卖单数据（最多前5档）
            List<DepthDataDTO.PriceQuantity> asks = depthDataDTO.getAsks();
            int askSize = Math.min(asks.size(), 5);
            for (int i = 0; i < askSize; i++) {
                DepthDataDTO.PriceQuantity level = asks.get(i);
                point.addField("ask_price_" + i, level.getPrice());
                point.addField("ask_qty_" + i, level.getQuantity());
            }
            
            // 添加买单数据（最多前5档）
            List<DepthDataDTO.PriceQuantity> bids = depthDataDTO.getBids();
            int bidSize = Math.min(bids.size(), 5);
            for (int i = 0; i < bidSize; i++) {
                DepthDataDTO.PriceQuantity level = bids.get(i);
                point.addField("bid_price_" + i, level.getPrice());
                point.addField("bid_qty_" + i, level.getQuantity());
            }
            
            // 计算买一卖一价差
            if (!asks.isEmpty() && !bids.isEmpty()) {
                point.addField("spread", asks.get(0).getPrice().subtract(bids.get(0).getPrice()));
            }
            
            // 添加到队列
            pointQueue.offer(point);
            
            // 如果队列超过批处理大小，则触发批处理
            if (pointQueue.size() >= marketDataConfig.getInfluxDbBatchSize()) {
                virtualThreadExecutor.submit(this::processBatch);
            }
        } catch (Exception e) {
            log.error("存储订单簿数据到InfluxDB异常: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage(), e);
        }
    }

    /**
     * 存储交易数据到InfluxDB
     *
     * @param tradeDTO 交易数据DTO
     */
    public void saveTradeData(TradeDTO tradeDTO) {
        try {
            // 创建数据点
            Point point = Point.measurement("trade")
                    .addTag("symbol", tradeDTO.getSymbol())
                    .addTag("maker", Boolean.toString(tradeDTO.isBuyerMaker()))
                    .addField("trade_id", tradeDTO.getId())
                    .addField("price", tradeDTO.getPrice())
                    .addField("quantity", tradeDTO.getQuantity())
                    .addField("quote_quantity", tradeDTO.getQuoteQuantity())
                    .time(tradeDTO.getTime().toInstant(ZoneOffset.UTC), WritePrecision.NS);
            
            // 添加到队列
            pointQueue.offer(point);
            
            // 如果队列超过批处理大小，则触发批处理
            if (pointQueue.size() >= marketDataConfig.getInfluxDbBatchSize()) {
                virtualThreadExecutor.submit(this::processBatch);
            }
        } catch (Exception e) {
            log.error("存储交易数据到InfluxDB异常: symbol={}, id={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理批次
     * 将队列中的数据点批量写入InfluxDB，根据数据类型分别存储到对应的专用桶中
     */
    private synchronized void processBatch() {
        try {
            if (pointQueue.isEmpty()) {
                return;
            }
            
            // 检查写入API是否可用
            if (writeApi == null) {
                log.warn("InfluxDB写入API不可用，清空队列中的{}条数据点", pointQueue.size());
                pointQueue.clear();
                return;
            }
            
            List<Point> batch = new ArrayList<>(marketDataConfig.getInfluxDbBatchSize());
            pointQueue.drainTo(batch, marketDataConfig.getInfluxDbBatchSize());
            
            if (!batch.isEmpty()) {
                // 按数据类型分组
                Map<String, List<Point>> pointsByMeasurement = new HashMap<>();
                
                // 将数据点按测量类型(measurement)分组
                for (Point point : batch) {
                    // 通过反射获取measurement值
                    String measurement;
                    try {
                        // 尝试从Point对象中获取measurement值
                        // 由于Point类可能没有直接的getMeasurement方法，使用字段名判断
                        if (point.toString().contains("kline")) {
                            measurement = "kline";
                        } else if (point.toString().contains("trade")) {
                            measurement = "trade";
                        } else if (point.toString().contains("depth") || point.toString().contains("order_book")) {
                            measurement = "depth";
                        } else {
                            measurement = "unknown";
                        }
                    } catch (Exception e) {
                        log.warn("无法获取数据点的measurement类型，使用默认桶: {}", e.getMessage());
                        measurement = "unknown";
                    }
                    pointsByMeasurement.computeIfAbsent(measurement, k -> new ArrayList<>()).add(point);
                }
                
                // 根据不同类型的数据写入到对应的专用桶
                for (Map.Entry<String, List<Point>> entry : pointsByMeasurement.entrySet()) {
                    String measurement = entry.getKey();
                    List<Point> points = entry.getValue();
                    
                    // 根据测量类型选择对应的存储桶
                    String bucketName;
                    switch (measurement) {
                        case "kline":
                            bucketName = "kline_data";
                            break;
                        case "trade":
                            bucketName = "trade_data";
                            break;
                        case "depth":
                        case "order_book":
                            bucketName = "depth_data";
                            break;
                        default:
                            bucketName = influxDBConfig.getBucket(); // 默认桶
                            break;
                    }
                    
                    try {
                        log.debug("批量写入{}条{}类型数据到InfluxDB桶: {}", 
                                points.size(), measurement, bucketName);
                        writeApi.writePoints(bucketName, influxDBConfig.getOrg(), points);
                    } catch (Exception e) {
                        log.error("写入{}类型数据到InfluxDB桶{}失败: {}", 
                                measurement, bucketName, e.getMessage(), e);
                        // 即使写入失败，我们也继续处理其他类型的数据，不抛出异常
                    }
                }
                
                log.debug("批量处理完成，共处理{}条数据", batch.size());
            }
        } catch (Exception e) {
            log.error("批量处理数据点时发生异常: {}", e.getMessage(), e);
            // 捕获所有异常，确保定时任务不会因为异常而停止
        }
    }

    /**
     * 检查K线数据是否存在
     *
     * @param symbol    交易对
     * @param interval  K线间隔
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 是否存在数据
     */
    public boolean hasKlineData(String symbol, String interval, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 将LocalDateTime转换为RFC3339格式的字符串，InfluxDB Flux查询需要此格式
            // 注意：不要使用双引号包裹时间字符串，Flux中的range函数需要时间值而非字符串
            String startTimeStr = startTime.atZone(ZoneOffset.UTC).toInstant().toString();
            String endTimeStr = endTime.atZone(ZoneOffset.UTC).toInstant().toString();
            
            // 简化查询，减少数据处理量以避免超时
            String query = String.format(
                    "from(bucket: \"%s\") " +
                    "|> range(start: %s, stop: %s) " +
                    "|> filter(fn: (r) => r._measurement == \"kline\") " +
                    "|> filter(fn: (r) => r.symbol == \"%s\") " +
                    "|> filter(fn: (r) => r.interval == \"%s\") " +
                    "|> limit(n: 1) " +
                    "|> yield(name: \"count\")",
                    "kline_data",  // 使用专用的K线数据桶
                    startTimeStr,
                    endTimeStr,
                    symbol,
                    interval
            );

            // 设置较短的查询超时来避免长时间阻塞
            List<FluxTable> tables = queryApi.query(query, influxDBConfig.getOrg());
            return !tables.isEmpty() && !tables.get(0).getRecords().isEmpty();
        } catch (Exception e) {
            log.error("检查K线数据异常: symbol={}, interval={}, error={}", symbol, interval, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查交易数据是否存在
     *
     * @param symbol    交易对
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 是否存在数据
     */
    public boolean hasTradeData(String symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 将LocalDateTime转换为RFC3339格式的字符串，不包裹双引号
            String startTimeStr = startTime.atZone(ZoneOffset.UTC).toInstant().toString();
            String endTimeStr = endTime.atZone(ZoneOffset.UTC).toInstant().toString();
            
            // 简化查询，减少数据处理量
            String query = String.format(
                    "from(bucket: \"%s\") " +
                    "|> range(start: %s, stop: %s) " +
                    "|> filter(fn: (r) => r._measurement == \"trade\") " +
                    "|> filter(fn: (r) => r.symbol == \"%s\") " +
                    "|> limit(n: 1) " +
                    "|> yield(name: \"count\")",
                    "trade_data",  // 使用专用的交易数据桶
                    startTimeStr,
                    endTimeStr,
                    symbol
            );

            // 使用增加后的超时时间进行查询
            List<FluxTable> tables = queryApi.query(query, influxDBConfig.getOrg());
            return !tables.isEmpty() && !tables.get(0).getRecords().isEmpty();
        } catch (Exception e) {
            log.error("检查交易数据异常: symbol={}, error={}", symbol, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 查询时间范围内的K线数据
     *
     * @param symbol    交易对
     * @param interval  K线间隔
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return K线数据列表
     */
    public List<KlineDataDTO> queryKlineData(String symbol, String interval, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 将LocalDateTime转换为RFC3339格式的字符串，不包裹双引号
            String startTimeStr = startTime.atZone(ZoneOffset.UTC).toInstant().toString();
            String endTimeStr = endTime.atZone(ZoneOffset.UTC).toInstant().toString();
            
            String query = String.format(
                    "from(bucket: \"%s\") " +
                    "|> range(start: %s, stop: %s) " +
                    "|> filter(fn: (r) => r._measurement == \"kline\") " +
                    "|> filter(fn: (r) => r.symbol == \"%s\") " +
                    "|> filter(fn: (r) => r.interval == \"%s\")",
                    "kline_data",  // 使用专用的K线数据桶
                    startTimeStr,
                    endTimeStr,
                    symbol,
                    interval
            );

            List<FluxTable> tables = queryApi.query(query, influxDBConfig.getOrg());
            List<KlineDataDTO> klines = new ArrayList<>();
            
            // 使用Map按时间分组收集每个K线的字段
            Map<Instant, Map<String, Object>> klineMap = new HashMap<>();
            
            // 处理查询结果
            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    Instant time = record.getTime();
                    String field = record.getField();
                    Object value = record.getValue();
                    
                    // 将每条记录按时间分组
                    Map<String, Object> fieldMap = klineMap.computeIfAbsent(time, k -> new HashMap<>());
                    fieldMap.put(field, value);
                }
            }
            
            // 将分组后的数据转换为KlineDataDTO对象
            for (Map.Entry<Instant, Map<String, Object>> entry : klineMap.entrySet()) {
                Instant time = entry.getKey();
                Map<String, Object> fields = entry.getValue();
                
                KlineDataDTO kline = new KlineDataDTO();
                kline.setSymbol(symbol);
                kline.setInterval(interval);
                
                // 设置时间
                LocalDateTime openTime = LocalDateTime.ofInstant(time, ZoneId.systemDefault());
                kline.setOpenTime(openTime);
                kline.setCloseTime(openTime); // 使用同一时间，实际应该根据interval计算
                
                // 设置价格和交易量字段
                if (fields.containsKey("open")) {
                    kline.setOpen(new BigDecimal(fields.get("open").toString()));
                }
                if (fields.containsKey("high")) {
                    kline.setHigh(new BigDecimal(fields.get("high").toString()));
                }
                if (fields.containsKey("low")) {
                    kline.setLow(new BigDecimal(fields.get("low").toString()));
                }
                if (fields.containsKey("close")) {
                    kline.setClose(new BigDecimal(fields.get("close").toString()));
                }
                if (fields.containsKey("volume")) {
                    kline.setVolume(new BigDecimal(fields.get("volume").toString()));
                }
                if (fields.containsKey("quote_volume")) {
                    kline.setQuoteAssetVolume(new BigDecimal(fields.get("quote_volume").toString()));
                }
                if (fields.containsKey("trades")) {
                    kline.setNumberOfTrades(((Number)fields.get("trades")).longValue());
                }
                if (fields.containsKey("closed")) {
                    kline.setClosed((Boolean)fields.get("closed"));
                }
                
                klines.add(kline);
            }
            
            log.info("从InfluxDB查询K线数据成功: symbol={}, interval={}, count={}", 
                    symbol, interval, klines.size());
            
            return klines;
        } catch (Exception e) {
            log.error("查询K线数据异常: symbol={}, interval={}, error={}", symbol, interval, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询时间范围内的交易数据
     *
     * @param symbol    交易对
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 交易数据列表
     */
    public List<TradeDTO> queryTradeData(String symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 将LocalDateTime转换为RFC3339格式的字符串，使用双引号包裹
            String startTimeStr = String.format("\"%s\"", startTime.atZone(ZoneOffset.UTC).toInstant().toString());
            String endTimeStr = String.format("\"%s\"", endTime.atZone(ZoneOffset.UTC).toInstant().toString());
            
            String query = String.format(
                    "from(bucket: \"%s\") " +
                    "|> range(start: %s, stop: %s) " +
                    "|> filter(fn: (r) => r._measurement == \"trade\") " +
                    "|> filter(fn: (r) => r.symbol == \"%s\")",
                    "trade_data",  // 使用专用的交易数据桶
                    startTimeStr,
                    endTimeStr,
                    symbol
            );

            List<FluxTable> tables = queryApi.query(query, influxDBConfig.getOrg());
            List<TradeDTO> trades = new ArrayList<>();
            
            // 使用Map按时间分组收集每个交易的字段
            Map<Instant, Map<String, Object>> tradeMap = new HashMap<>();
            Map<Instant, String> makerMap = new HashMap<>();
            
            // 处理查询结果
            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    Instant time = record.getTime();
                    String field = record.getField();
                    Object value = record.getValue();
                    
                    // 将每条记录按时间分组
                    Map<String, Object> fieldMap = tradeMap.computeIfAbsent(time, k -> new HashMap<>());
                    fieldMap.put(field, value);
                    
                    // 保存标签信息
                    if (record.getValueByKey("maker") != null) {
                        makerMap.put(time, record.getValueByKey("maker").toString());
                    }
                }
            }
            
            // 将分组后的数据转换为TradeDTO对象
            for (Map.Entry<Instant, Map<String, Object>> entry : tradeMap.entrySet()) {
                Instant time = entry.getKey();
                Map<String, Object> fields = entry.getValue();
                
                TradeDTO trade = new TradeDTO();
                trade.setSymbol(symbol);
                
                // 设置时间
                LocalDateTime tradeTime = LocalDateTime.ofInstant(time, ZoneId.systemDefault());
                trade.setTime(tradeTime);
                
                // 设置交易字段
                if (fields.containsKey("trade_id")) {
                    trade.setId(((Number)fields.get("trade_id")).longValue());
                }
                if (fields.containsKey("price")) {
                    trade.setPrice(new BigDecimal(fields.get("price").toString()));
                }
                if (fields.containsKey("quantity")) {
                    trade.setQuantity(new BigDecimal(fields.get("quantity").toString()));
                }
                if (fields.containsKey("quote_quantity")) {
                    trade.setQuoteQuantity(new BigDecimal(fields.get("quote_quantity").toString()));
                }
                
                // 设置买方做市商标志
                String maker = makerMap.get(time);
                if (maker != null) {
                    trade.setBuyerMaker(Boolean.parseBoolean(maker));
                }
                
                trades.add(trade);
            }
            
            log.info("从InfluxDB查询交易数据成功: symbol={}, count={}", symbol, trades.size());
            
            return trades;
        } catch (Exception e) {
            log.error("查询交易数据异常: symbol={}, error={}", symbol, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询时间范围内的订单簿数据
     *
     * @param symbol    交易对
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 订单簿数据列表
     */
    public List<DepthDataDTO> queryOrderBookData(String symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 将LocalDateTime转换为RFC3339格式的字符串，使用双引号包裹
            String startTimeStr = String.format("\"%s\"", startTime.atZone(ZoneOffset.UTC).toInstant().toString());
            String endTimeStr = String.format("\"%s\"", endTime.atZone(ZoneOffset.UTC).toInstant().toString());
            
            String query = String.format(
                    "from(bucket: \"%s\") " +
                    "|> range(start: %s, stop: %s) " +
                    "|> filter(fn: (r) => r._measurement == \"order_book\") " +
                    "|> filter(fn: (r) => r.symbol == \"%s\")",
                    "depth_data",  // 使用专用的深度数据桶
                    startTimeStr,
                    endTimeStr,
                    symbol
            );

            List<FluxTable> tables = queryApi.query(query, influxDBConfig.getOrg());
            List<DepthDataDTO> depthDataList = new ArrayList<>();
            
            // 使用Map按时间分组收集每个深度数据的字段
            Map<Instant, Map<String, Object>> depthMap = new HashMap<>();
            
            // 处理查询结果
            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    Instant time = record.getTime();
                    String field = record.getField();
                    Object value = record.getValue();
                    
                    // 将每条记录按时间分组
                    Map<String, Object> fieldMap = depthMap.computeIfAbsent(time, k -> new HashMap<>());
                    fieldMap.put(field, value);
                }
            }
            
            // 将分组后的数据转换为DepthDataDTO对象
            // 注意：这里只是一个简化的实现，实际场景中需要解析具体的买单卖单数据
            for (Map.Entry<Instant, Map<String, Object>> entry : depthMap.entrySet()) {
                Instant time = entry.getKey();
                Map<String, Object> fields = entry.getValue();
                
                DepthDataDTO depthData = new DepthDataDTO();
                depthData.setSymbol(symbol);
                
                // 设置时间
                LocalDateTime updateTime = LocalDateTime.ofInstant(time, ZoneId.systemDefault());
                depthData.setUpdateTime(updateTime);
                
                // 设置lastUpdateId
                if (fields.containsKey("lastUpdateId")) {
                    depthData.setLastUpdateId(((Number)fields.get("lastUpdateId")).longValue());
                }
                
                // 设置深度
                if (fields.containsKey("depth")) {
                    depthData.setLimit(((Number)fields.get("depth")).intValue());
                }
                
                // 在实际实现中，你需要反序列化存储的买单卖单JSON数据
                // 这里简化处理，实际生产中应该有专门的序列化/反序列化逻辑
                List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
                List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
                
                if (fields.containsKey("bids") && fields.get("bids") instanceof String) {
                    String bidsJson = (String) fields.get("bids");
                    // 解析bidsJson为List<DepthDataDTO.PriceQuantity>
                    // 这里需要你的JSON解析逻辑
                }
                
                if (fields.containsKey("asks") && fields.get("asks") instanceof String) {
                    String asksJson = (String) fields.get("asks");
                    // 解析asksJson为List<DepthDataDTO.PriceQuantity>
                    // 这里需要你的JSON解析逻辑
                }
                
                depthData.setBids(bids);
                depthData.setAsks(asks);
                
                depthDataList.add(depthData);
            }
            
            log.info("从InfluxDB查询订单簿数据成功: symbol={}, count={}", symbol, depthDataList.size());
            
            return depthDataList;
        } catch (Exception e) {
            log.error("查询订单簿数据异常: symbol={}, error={}", symbol, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}