package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * TradeDataProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TradeDataProcessorTest {

    @Mock
    private KafkaMessageProducer kafkaMessageProducer;

    @Mock
    private InfluxDBRepository influxDBRepository;

    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private MarketDataConverter marketDataConverter;
    
    @Mock
    private ExecutorService virtualThreadExecutor;

    @InjectMocks
    private TradeDataProcessor tradeDataProcessor;

    private TradeDTO tradeDTO;
    private String tradeMessage;

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据
        tradeDTO = new TradeDTO(
                987654321L,
                "BTCUSDT",
                new BigDecimal("35000.00"),
                new BigDecimal("0.5"),
                new BigDecimal("17500.00"),
                LocalDateTime.now(),
                false,
                true
        );
        
        tradeMessage = "{\"symbol\":\"BTCUSDT\",\"price\":\"35000.00\",\"quantity\":\"0.5\"}";

        // 配置mock行为，使用doReturn语法
        doReturn("{\"test\":\"json\"}").when(objectMapper).writeValueAsString(any(TradeDTO.class));
        doReturn(tradeDTO).when(marketDataConverter).convertAggTradeMessageToTradeDTO(anyString());
        
        // 配置虚拟线程执行器，立即执行提交的任务
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(virtualThreadExecutor).submit(any(Runnable.class));
    }

    @Test
    void testProcessTradeData() throws Exception {
        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);

        // 验证发布到Kafka
        verify(kafkaMessageProducer, timeout(1000)).sendTradeData(eq("BTCUSDT"), eq("{\"test\":\"json\"}"));

        // 验证存储到InfluxDB
        verify(influxDBRepository, timeout(1000)).saveTradeData(eq(tradeDTO));
    }

    @Test
    void testProcessTradeDataWithKafkaException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("Kafka error")).when(kafkaMessageProducer).sendTradeData(any(), any());

        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);

        // 验证即使Kafka发布失败，也会尝试存储到InfluxDB
        verify(influxDBRepository, timeout(1000)).saveTradeData(eq(tradeDTO));
    }

    @Test
    void testProcessTradeDataWithInfluxDBException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("InfluxDB error")).when(influxDBRepository).saveTradeData(any());

        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);

        // 验证即使InfluxDB存储失败，也会尝试发布到Kafka
        verify(kafkaMessageProducer, timeout(1000)).sendTradeData(eq("BTCUSDT"), eq("{\"test\":\"json\"}"));
    }
    
    @Test
    void testProcessTradeDataWithJsonException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("JSON error")).when(objectMapper).writeValueAsString(any());
        
        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);
        
        // 验证即使JSON序列化失败，也会尝试存储到InfluxDB
        verify(influxDBRepository, timeout(1000)).saveTradeData(eq(tradeDTO));
        
        // 验证不会尝试发布到Kafka
        verify(kafkaMessageProducer, never()).sendTradeData(any(), any());
    }
} 