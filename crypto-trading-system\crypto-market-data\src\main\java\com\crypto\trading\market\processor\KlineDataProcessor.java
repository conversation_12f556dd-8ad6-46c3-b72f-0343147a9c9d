package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

/**
 * K线数据处理器
 * 负责处理K线数据，包括发布到Kafka和存储到InfluxDB和MySQL
 * 使用JDK21虚拟线程提高性能
 */
@Component
public class KlineDataProcessor {

    private static final Logger log = LoggerFactory.getLogger(KlineDataProcessor.class);

    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;

    @Autowired
    private InfluxDBRepository influxDBRepository;
    
    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private MarketDataConverter marketDataConverter;

    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 虚拟线程执行器
     * 通过配置注入，避免每个处理器创建自己的执行器
     */
    @Autowired
    private ExecutorService virtualThreadExecutor;

    /**
     * 处理K线数据
     *
     * @param message WebSocket K线数据消息
     * @param interval K线间隔
     */
    public void processKlineData(WebSocketMessage<KlineData> message, String interval) {
        if (message == null || message.getData() == null) {
            log.warn("收到空的K线数据消息");
            return;
        }

        KlineData klineData = message.getData();
        String symbol = message.getSymbol();

        log.debug("处理K线数据: symbol={}, interval={}, openTime={}, closeTime={}",
                symbol, interval, klineData.getOpenTime(), klineData.getCloseTime());

        try {
            // 使用虚拟线程异步处理
            virtualThreadExecutor.submit(() -> {
                try {
                    // 转换为KlineDataDTO
                    KlineDataDTO klineDataDTO = marketDataConverter.convertToKlineDataDTO(klineData, symbol, interval);
                    
                    // 提高系统容错性，即使一个操作失败，其他操作仍会执行
                    // 发布到Kafka
                    try {
                        publishToKafka(klineDataDTO);
                    } catch (Exception e) {
                        log.error("发布K线数据到Kafka异常，但将继续处理: symbol={}, interval={}, error={}", 
                                symbol, interval, e.getMessage(), e);
                    }
                    
                    // 存储到InfluxDB
                    try {
                        saveToInfluxDB(klineDataDTO);
                    } catch (Exception e) {
                        log.error("存储K线数据到InfluxDB异常，但将继续处理: symbol={}, interval={}, error={}", 
                                symbol, interval, e.getMessage(), e);
                    }
                    
                    // 存储到MySQL
                    try {
                        saveToMySQL(klineDataDTO);
                    } catch (Exception e) {
                        log.error("存储K线数据到MySQL异常: symbol={}, interval={}, error={}", 
                                symbol, interval, e.getMessage(), e);
                    }
                } catch (Exception e) {
                    log.error("处理K线数据异常: symbol={}, interval={}, error={}", 
                            symbol, interval, e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("提交K线数据处理任务异常，但应用程序将继续运行: symbol={}, interval={}, error={}", 
                    symbol, interval, e.getMessage(), e);
        }
    }

    /**
     * 发布K线数据到Kafka
     *
     * @param klineDataDTO K线数据DTO
     */
    private void publishToKafka(KlineDataDTO klineDataDTO) {
        try {
            // 将K线数据转换为JSON字符串
            String message = objectMapper.writeValueAsString(klineDataDTO);
            
            // 构建Kafka消息的Key
            String key = klineDataDTO.getSymbol() + "_" + klineDataDTO.getInterval();
            
            // 发布消息到Kafka
            kafkaMessageProducer.sendKlineData(key, message);
            
            log.debug("已发布K线数据到Kafka: symbol={}, interval={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval());
        } catch (Exception e) {
            log.error("发布K线数据到Kafka异常: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
        }
    }

    /**
     * 保存K线数据到InfluxDB
     *
     * @param klineDataDTO K线数据DTO
     */
    private void saveToInfluxDB(KlineDataDTO klineDataDTO) {
        try {
            // 存储到InfluxDB
            influxDBRepository.saveKlineData(klineDataDTO);
            
            log.debug("已保存K线数据到InfluxDB: symbol={}, interval={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval());
        } catch (Exception e) {
            log.error("保存K线数据到InfluxDB异常: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
        }
    }
    
    /**
     * 保存K线数据到MySQL
     *
     * @param klineDataDTO K线数据DTO
     */
    private void saveToMySQL(KlineDataDTO klineDataDTO) {
        try {
            // 存储到MySQL
            mySQLMarketDataRepository.saveKlineData(klineDataDTO);
            
            log.debug("已保存K线数据到MySQL: symbol={}, interval={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval());
        } catch (Exception e) {
            log.error("保存K线数据到MySQL异常: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
        }
    }
} 