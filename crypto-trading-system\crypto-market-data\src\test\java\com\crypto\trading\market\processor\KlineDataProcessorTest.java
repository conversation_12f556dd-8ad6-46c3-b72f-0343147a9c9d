package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.ExecutorService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * KlineDataProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class KlineDataProcessorTest {

    @Mock
    private KafkaMessageProducer kafkaMessageProducer;

    @Mock
    private InfluxDBRepository influxDBRepository;
    
    @Mock
    private MarketDataConverter marketDataConverter;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private ExecutorService virtualThreadExecutor;

    @InjectMocks
    private KlineDataProcessor klineDataProcessor;

    private WebSocketMessage<KlineData> klineMessage;
    private KlineDataDTO klineDataDTO;
    private String interval = "1m";

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据 - 创建WebSocketMessage<KlineData>
        KlineData klineData = new KlineData();
        klineData.setOpenTime(1622535600000L);
        klineData.setCloseTime(1622535659999L);
        klineData.setOpen(new BigDecimal("35000.00"));
        klineData.setHigh(new BigDecimal("35100.00"));
        klineData.setLow(new BigDecimal("34900.00"));
        klineData.setClose(new BigDecimal("35050.00"));
        klineData.setVolume(new BigDecimal("10.5"));
        klineData.setQuoteAssetVolume(new BigDecimal("367500.00"));
        klineData.setNumberOfTrades(150L);
        
        klineMessage = new WebSocketMessage<>("kline", 1622535659999L, "BTCUSDT", klineData);
        
        // 准备KlineDataDTO
        klineDataDTO = new KlineDataDTO(
            "BTCUSDT",
            interval,
            LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535600000L), ZoneId.systemDefault()),
            LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535659999L), ZoneId.systemDefault()),
            new BigDecimal("35000.00"),
            new BigDecimal("35100.00"),
            new BigDecimal("34900.00"),
            new BigDecimal("35050.00"),
            new BigDecimal("10.5"),
            new BigDecimal("367500.00"),
            150L,
            new BigDecimal("5.2"),
            new BigDecimal("180000.00"),
            true
        );
        
        // 配置mock行为，使用doReturn语法
        doReturn(klineDataDTO).when(marketDataConverter).convertToKlineDataDTO(any(KlineData.class), eq("BTCUSDT"), eq(interval));
        doReturn("{\"test\":\"json\"}").when(objectMapper).writeValueAsString(any(KlineDataDTO.class));
        
        // 配置虚拟线程执行器，立即执行提交的任务
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(virtualThreadExecutor).submit(any(Runnable.class));
    }

    @Test
    void testProcessKlineData() throws Exception {
        // 调用被测试方法
        klineDataProcessor.processKlineData(klineMessage, interval);

        // 验证发布到Kafka
        verify(kafkaMessageProducer).sendKlineData(eq("BTCUSDT_1m"), eq("{\"test\":\"json\"}"));

        // 验证存储到InfluxDB
        verify(influxDBRepository).saveKlineData(eq(klineDataDTO));
    }

    @Test
    void testProcessKlineDataWithKafkaException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("Kafka error")).when(kafkaMessageProducer).sendKlineData(any(), any());

        // 调用被测试方法
        klineDataProcessor.processKlineData(klineMessage, interval);

        // 验证即使Kafka发布失败，也会尝试存储到InfluxDB
        verify(influxDBRepository).saveKlineData(eq(klineDataDTO));
    }

    @Test
    void testProcessKlineDataWithInfluxDBException() throws Exception {
        // 配置mock抛出异常
        doThrow(new RuntimeException("InfluxDB error")).when(influxDBRepository).saveKlineData(any());

        // 调用被测试方法
        klineDataProcessor.processKlineData(klineMessage, interval);

        // 验证即使InfluxDB存储失败，也会尝试发布到Kafka
        verify(kafkaMessageProducer).sendKlineData(eq("BTCUSDT_1m"), eq("{\"test\":\"json\"}"));
    }
} 