package com.crypto.trading.bootstrap.lifecycle;

import com.crypto.trading.bootstrap.order.StartupOrderManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 关闭钩子
 * 负责在应用程序关闭时执行清理操作
 */
@Component
public class ShutdownHook implements InitializingBean, DisposableBean {
    
    private static final Logger log = LoggerFactory.getLogger(ShutdownHook.class);
    
    /**
     * 启动顺序管理器
     */
    private final StartupOrderManager startupOrderManager;
    
    /**
     * 关闭标志
     */
    private final AtomicBoolean shuttingDown = new AtomicBoolean(false);
    
    /**
     * 构造函数
     * 
     * @param startupOrderManager 启动顺序管理器
     */
    public ShutdownHook(StartupOrderManager startupOrderManager) {
        this.startupOrderManager = startupOrderManager;
    }
    
    @Override
    public void afterPropertiesSet() {
        // 注册JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(this::performShutdown, "ShutdownHookThread"));
        log.info("已注册JVM关闭钩子");
    }
    
    @Override
    public void destroy() {
        performShutdown();
    }
    
    /**
     * 执行关闭操作
     */
    public void performShutdown() {
        if (shuttingDown.compareAndSet(false, true)) {
            try {
                log.info("应用程序正在关闭...");
                
                // 关闭所有模块
                startupOrderManager.shutdownAll();
                
                // 清理线程本地变量
                startupOrderManager.clearThreadLocal();
                
                log.info("应用程序已关闭");
            } catch (Exception e) {
                log.error("应用程序关闭过程中发生错误: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 检查是否正在关闭
     * 
     * @return 如果正在关闭，返回true；否则返回false
     */
    public boolean isShuttingDown() {
        return shuttingDown.get();
    }
}