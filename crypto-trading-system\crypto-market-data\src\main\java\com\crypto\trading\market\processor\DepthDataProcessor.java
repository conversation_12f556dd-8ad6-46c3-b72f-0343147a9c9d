package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

/**
 * 深度数据处理器
 * 负责处理深度数据，包括发布到Kafka和存储到InfluxDB和MySQL
 * 使用JDK21虚拟线程提高性能
 */
@Component
public class DepthDataProcessor {

    private static final Logger log = LoggerFactory.getLogger(DepthDataProcessor.class);

    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;

    @Autowired
    private InfluxDBRepository influxDBRepository;
    
    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private MarketDataConverter marketDataConverter;

    /**
     * 虚拟线程执行器
     * 通过配置注入，避免每个处理器创建自己的执行器
     */
    @Autowired
    private ExecutorService virtualThreadExecutor;

    /**
     * 处理深度数据
     *
     * @param message WebSocket深度数据消息
     */
    public void processDepthData(WebSocketMessage<DepthData> message) {
        if (message == null || message.getData() == null) {
            log.warn("收到空的深度数据消息");
            return;
        }

        DepthData depthData = message.getData();
        String symbol = message.getSymbol();
        
        log.debug("处理深度数据: symbol={}, lastUpdateId={}, bids={}, asks={}",
                symbol,
                depthData.getLastUpdateId(),
                depthData.getBids() != null ? depthData.getBids().size() : 0,
                depthData.getAsks() != null ? depthData.getAsks().size() : 0);

        try {
            // 使用虚拟线程异步处理
            virtualThreadExecutor.submit(() -> {
                try {
                    // 转换为OrderBookDTO
                    DepthDataDTO depthDataDTO = marketDataConverter.convertToOrderBookDTO(depthData, symbol);
                    
                    // 提高系统容错性，即使一个操作失败，其他操作仍会执行
                    // 发布到Kafka
                    try {
                        publishToKafka(depthDataDTO);
                    } catch (Exception e) {
                        log.error("发布深度数据到Kafka异常，但将继续处理: symbol={}, error={}", 
                                symbol, e.getMessage(), e);
                    }
                    
                    // 存储到InfluxDB
                    try {
                        storeToInfluxDB(depthDataDTO);
                    } catch (Exception e) {
                        log.error("存储深度数据到InfluxDB异常，但将继续处理: symbol={}, error={}", 
                                symbol, e.getMessage(), e);
                    }
                    
                    // 存储到MySQL
                    try {
                        storeToMySQL(depthDataDTO);
                    } catch (Exception e) {
                        log.error("存储深度数据到MySQL异常: symbol={}, error={}", 
                                symbol, e.getMessage(), e);
                    }
                    
                } catch (Exception e) {
                    log.error("处理深度数据异常: symbol={}, error={}", symbol, e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("提交深度数据处理任务异常，但应用程序将继续运行: symbol={}, error={}", symbol, e.getMessage(), e);
        }
    }

    /**
     * 发布深度数据到Kafka
     *
     * @param depthDataDTO 订单簿DTO
     */
    private void publishToKafka(DepthDataDTO depthDataDTO) {
        try {
            // 将深度数据转换为JSON
            String message = objectMapper.writeValueAsString(depthDataDTO);

            // 使用交易对作为消息键
            String key = depthDataDTO.getSymbol();

            // 发布到Kafka
            kafkaMessageProducer.sendDepthData(key, message);

            log.debug("深度数据已发布到Kafka: symbol={}", depthDataDTO.getSymbol());
        } catch (Exception e) {
            log.error("发布深度数据到Kafka异常: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage(), e);
        }
    }

    /**
     * 存储深度数据到InfluxDB
     *
     * @param depthDataDTO 订单簿DTO
     */
    private void storeToInfluxDB(DepthDataDTO depthDataDTO) {
        try {
            // 存储到InfluxDB
            influxDBRepository.saveOrderBookData(depthDataDTO);

            log.debug("深度数据已存储到InfluxDB: symbol={}", depthDataDTO.getSymbol());
        } catch (Exception e) {
            log.error("存储深度数据到InfluxDB异常: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage(), e);
        }
    }
    
    /**
     * 存储深度数据到MySQL
     *
     * @param depthDataDTO 订单簿DTO
     */
    private void storeToMySQL(DepthDataDTO depthDataDTO) {
        try {
            // 存储到MySQL
            mySQLMarketDataRepository.saveDepthData(depthDataDTO);

            log.debug("深度数据已存储到MySQL: symbol={}", depthDataDTO.getSymbol());
        } catch (Exception e) {
            log.error("存储深度数据到MySQL异常: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage(), e);
        }
    }
}